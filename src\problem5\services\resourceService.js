const database = require('../configs/database');

class ResourceService {
    
    // Get all resources with optional filtering
    async getAllResources(filters = {}) {
        try {
            let sql = 'SELECT * FROM resources WHERE 1=1';
            const params = [];

            // Filter by category
            if (filters.category) {
                sql += ' AND category LIKE ?';
                params.push(`%${filters.category}%`);
            }

            // Filter by status
            if (filters.status) {
                sql += ' AND status = ?';
                params.push(filters.status);
            }

            // Search in name and description
            if (filters.search) {
                sql += ' AND (name LIKE ? OR description LIKE ?)';
                const searchTerm = `%${filters.search}%`;
                params.push(searchTerm, searchTerm);
            }

            sql += ' ORDER BY created_at DESC';

            const resources = await database.all(sql, params);

            return {
                success: true,
                data: resources,
                total: resources.length
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    // Get resource by ID
    async getResourceById(id) {
        try {
            const resource = await database.get('SELECT * FROM resources WHERE id = ?', [id]);

            if (!resource) {
                return {
                    success: false,
                    error: 'Resource not found'
                };
            }

            return {
                success: true,
                data: resource
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    // Create new resource
    async createResource(data) {
        try {
            // Validation
            if (!data.name || data.name.trim() === '') {
                return {
                    success: false,
                    error: 'Name is required'
                };
            }

            if (!data.description) {
                return {
                    success: false,
                    error: 'Description is required'
                };
            }

            // Insert into database
            const sql = `
                INSERT INTO resources (name, description, category, status)
                VALUES (?, ?, ?, ?)
            `;
            const params = [
                data.name.trim(),
                data.description.trim(),
                data.category || 'General',
                data.status || 'active'
            ];

            const result = await database.run(sql, params);

            // Get the created resource
            const resource = await database.get('SELECT * FROM resources WHERE id = ?', [result.id]);

            return {
                success: true,
                data: resource,
                message: 'Resource created successfully'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    // Update resource (PUT - replace entire resource)
    async updateResource(id, data) {
        try {
            // Check if resource exists
            const existing = await database.get('SELECT * FROM resources WHERE id = ?', [id]);

            if (!existing) {
                return {
                    success: false,
                    error: 'Resource not found'
                };
            }

            // PUT requires name and description (core fields)
            if (!data.name || data.name.trim() === '') {
                return {
                    success: false,
                    error: 'Name is required for PUT operation'
                };
            }

            if (!data.description || data.description.trim() === '') {
                return {
                    success: false,
                    error: 'Description is required for PUT operation'
                };
            }

            // PUT replaces the entire resource with provided data
            const sql = `
                UPDATE resources
                SET name = ?, description = ?, category = ?, status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;
            const params = [
                data.name.trim(),
                data.description.trim(),
                data.category || 'General',
                data.status || 'active',
                id
            ];

            await database.run(sql, params);

            // Get updated resource
            const resource = await database.get('SELECT * FROM resources WHERE id = ?', [id]);

            return {
                success: true,
                data: resource,
                message: 'Resource updated successfully'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Patch resource (PATCH - partial update)
    async patchResource(id, data) {
        try {
            // Check if resource exists
            const existing = await database.get('SELECT * FROM resources WHERE id = ?', [id]);

            if (!existing) {
                return {
                    success: false,
                    error: 'Resource not found'
                };
            }

            // Build update query for only provided fields
            const updates = [];
            const params = [];

            if (data.name !== undefined) {
                if (data.name.trim() === '') {
                    return {
                        success: false,
                        error: 'Name cannot be empty'
                    };
                }
                updates.push('name = ?');
                params.push(data.name.trim());
            }
            if (data.description !== undefined) {
                if (data.description.trim() === '') {
                    return {
                        success: false,
                        error: 'Description cannot be empty'
                    };
                }
                updates.push('description = ?');
                params.push(data.description.trim());
            }
            if (data.category !== undefined) {
                updates.push('category = ?');
                params.push(data.category || 'General');
            }
            if (data.status !== undefined) {
                // Validate status value
                if (!['active', 'inactive'].includes(data.status)) {
                    return {
                        success: false,
                        error: 'Status must be either "active" or "inactive"'
                    };
                }
                updates.push('status = ?');
                params.push(data.status);
            }

            if (updates.length === 0) {
                return {
                    success: true,
                    data: existing,
                    message: 'No changes made'
                };
            }

            updates.push('updated_at = CURRENT_TIMESTAMP');
            params.push(id);

            const sql = `UPDATE resources SET ${updates.join(', ')} WHERE id = ?`;
            await database.run(sql, params);

            // Get updated resource
            const resource = await database.get('SELECT * FROM resources WHERE id = ?', [id]);

            return {
                success: true,
                data: resource,
                message: 'Resource patched successfully'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    // Delete resource
    async deleteResource(id) {
        try {
            // Check if resource exists
            const existing = await database.get('SELECT * FROM resources WHERE id = ?', [id]);

            if (!existing) {
                return {
                    success: false,
                    error: 'Resource not found'
                };
            }

            await database.run('DELETE FROM resources WHERE id = ?', [id]);

            return {
                success: true,
                message: 'Resource deleted successfully'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Clear all resources (useful for testing)
    async clearAllResources() {
        try {
            return await database.clearAllData();
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = new ResourceService();
