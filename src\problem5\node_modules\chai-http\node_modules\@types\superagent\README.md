# Installation
> `npm install --save @types/superagent`

# Summary
This package contains type definitions for SuperAgent (https://github.com/visionmedia/superagent).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/superagent.

### Additional Details
 * Last updated: Thu, 16 Sep 2021 18:31:38 GMT
 * Dependencies: [@types/cookiejar](https://npmjs.com/package/@types/cookiejar), [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [<PERSON>](https://github.com/NicoZelaya), [<PERSON>](https://github.com/mxl), [<PERSON><PERSON> L<PERSON>rin<PERSON>](https://github.com/paplorinc), [<PERSON><PERSON><PERSON> <PERSON>](https://github.com/shreyjain1994), [<PERSON>op<PERSON>](https://github.com/zopf), [<PERSON>](https://github.com/beeequeue), [<PERSON><PERSON>](https://github.com/lukaselmer), [<PERSON>](https://github.com/theQuazz), [<PERSON>](https://github.com/carnesen), [<PERSON> Kindberg](https://github.com/ghostganz), and [LuckyWind_sck](https://github.com/LuckyWindsck).
