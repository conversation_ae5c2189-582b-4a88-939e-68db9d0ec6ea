const resourceService = require('../services/resourceService');

class ResourceController {
    
    // GET /api/resources
    async getAllResources(req, res) {
        try {
            const filters = {
                category: req.query.category,
                status: req.query.status,
                search: req.query.search
            };

            const result = await resourceService.getAllResources(filters);

            if (!result.success) {
                return res.status(500).json(result);
            }

            res.json({
                success: result.success,
                data: result.data,
                pagination: {
                    total: result.total,
                    count: result.data.length
                }
            });

        } catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
    
    // GET /api/resources/:id
    async getResourceById(req, res) {
        try {
            const { id } = req.params;
            const result = await resourceService.getResourceById(id);

            if (!result.success) {
                return res.status(404).json(result);
            }

            res.json(result);

        } catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
    
    // POST /api/resources
    async createResource(req, res) {
        try {
            const result = await resourceService.createResource(req.body);

            if (!result.success) {
                return res.status(400).json(result);
            }

            res.status(201).json(result);

        } catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
    
    // PUT /api/resources/:id
    async updateResource(req, res) {
        try {
            const { id } = req.params;
            const result = await resourceService.updateResource(id, req.body);

            if (!result.success) {
                const statusCode = result.error.includes('not found') ? 404 : 400;
                return res.status(statusCode).json(result);
            }

            res.json(result);

        } catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }

    // PATCH /api/resources/:id
    async patchResource(req, res) {
        try {
            const { id } = req.params;
            const result = await resourceService.patchResource(id, req.body);

            if (!result.success) {
                const statusCode = result.error.includes('not found') ? 404 : 400;
                return res.status(statusCode).json(result);
            }

            res.json(result);

        } catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }

    // DELETE /api/resources/:id
    async deleteResource(req, res) {
        try {
            const { id } = req.params;
            const result = await resourceService.deleteResource(id);

            if (!result.success) {
                return res.status(404).json(result);
            }

            res.json(result);

        } catch (error) {
            res.status(500).json({
                success: false,
                error: 'Internal server error'
            });
        }
    }
}

module.exports = new ResourceController();
